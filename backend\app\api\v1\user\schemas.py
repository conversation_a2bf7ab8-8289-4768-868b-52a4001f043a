"""
backend/app/api/v1/user/schemas.py
用户请求/响应模型
"""
from pydantic import BaseModel,Field

from app.schemas.base import BaseSchema
from app.schemas.auth import AuthUser

#用户登录模型
# 作用：用户登录请求时使用 
class UserLoginRequest(BaseModel):
    username: str = Field(..., min_length=2, max_length=20, example="admin", description="用户名")
    password: str = Field(..., min_length=6, example="123456", description="密码")

#用户基础模型
class UserBase(BaseModel):
    pass

#用户信息
class UserInfo(AuthUser):
    realname: str = Field(..., min_length=2, max_length=20, example="管理员", description="真实姓名")
    department_id: int = Field(..., example=1, description="部门ID")
    department_name: str = Field(..., example="管理员", description="部门名称")
    email: str | None = Field(None, example="<EMAIL>", description="邮箱")
    phone: str | None = Field(None, example="13900001111", description="手机号")
    avatar: str | None = Field(None, example="https://www.example.com/avatar.png", description="头像")
    last_login_at: str | None = Field(None, example="2021-01-01 00:00:00", description="最后登录时间")

# 用户创建请求模型
class UserCreateRequest(BaseModel):
    pass

