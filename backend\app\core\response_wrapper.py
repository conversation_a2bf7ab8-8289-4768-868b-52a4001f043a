"""
backend/app/core/response_wrapper.py
响应包装器
"""
from functools import wraps
from fastapi.responses import JSONResponse
from app.schemas.response import success_response

def wrap_response(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        result = await func(*args, **kwargs)
        if isinstance(result, JSONResponse):
            return result
        
        # 自动包装成功响应
        if hasattr(result, 'model_dump'):  # 如果是 Pydantic 模型
            data = result.model_dump()
        else:
            data = result
            
        response = success_response(data=data, message="请求成功")
        return JSONResponse(
            status_code=200,
            content=response.model_dump()
        )
    return wrapper