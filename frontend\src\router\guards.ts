/**
 * frontend/src/router/guards.ts
 * 路由守卫
 */

import type { Router } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { registerDynamicRoutes } from './dynamic'

export function setupRouterGuards(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore()

    console.log('路由守卫触发:', {
      path: to.path,
      name: to.name,
      isLoggedIn: userStore.isLoggedIn,
      dynamicRoutesInitialized: userStore.dynamicRoutesInitialized,
      requiresAuth: to.meta.requiresAuth
    })

    // 登录页和404页直接放行
    if (to.name === 'login' || to.name === 'not-found') {
      console.log('放行登录页或404页')
      next()
      return
    }

    // 如果用户未登录，跳转到登录页
    if (!userStore.isLoggedIn) {
      console.log('用户未登录，跳转到登录页')
      next({ name: 'login', query: { redirect: to.fullPath }, replace: true })
      return
    }

    // 用户已登录但动态路由未初始化，先注册动态路由
    if (!userStore.dynamicRoutesInitialized) {
      try {
        console.log('开始注册动态路由...')
        const success = await registerDynamicRoutes(router, userStore.menus)

        if (success) {
          userStore.setDynamicRoutesInitialized(true)
          console.log('动态路由注册成功，重新导航到:', to.fullPath)
          // 重新导航到目标路由，确保路由匹配成功
          next({ ...to, replace: true })
        } else {
          console.error('动态路由注册失败')
          next({ name: 'login', replace: true })
        }
        return
      } catch (error) {
        console.error('动态路由注册异常:', error)
        next({ name: 'login', replace: true })
        return
      }
    }

    // 动态路由已初始化，检查路由是否存在
    const routeExists = router.hasRoute(to.name as string)
    if (!routeExists && to.name !== 'not-found') {
      console.log('路由不存在，跳转到404:', to.path)
      next({ name: 'not-found', replace: true })
      return
    }

    // 正常放行
    console.log('正常放行:', to.path)
    next()
  })
}



  //每次路由跳转前都会执行。它接收三个参数：
  // to: 即将要进入的目标路由对象
  // from: 当前导航正要离开的路由
  // next: 一定要调用该方法来 resolve 这个钩子。执行效果依赖 next 方法的调用参数。
