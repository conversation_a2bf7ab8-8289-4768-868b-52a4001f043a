/**
 * frontend/src/router/guards.ts
 * 路由守卫
 */

import type { Router } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { initializeDynamicRoutes } from './index'


export function setupRouterGuards(router: Router) {
  router.beforeEach(async (to, from, next) => {
    if(to.name=="login"|| to.name=="not-found"){
      next()
      return
    }
     const userStore = useUserStore()
    if (to.meta.requiresAuth && !userStore.isLoggedIn) { //判断是否需要登录
      next({ name: 'login', query: { redirect: to.fullPath }, replace: true })
      return
    } 
    if (to.meta.requiresAuth && userStore.isLoggedIn && !userStore.dynamicRoutesInitialized) {
      try {
        await initializeDynamicRoutes(userStore.menus)
        next({ ...to, replace: true }) // 重新进入当前路由
      } catch (error) {
        console.error('动态路由注册失败:', error)
        next({ name: 'login', replace: true })
      }
    }
    next()
  })
  }



  //每次路由跳转前都会执行。它接收三个参数：
  // to: 即将要进入的目标路由对象
  // from: 当前导航正要离开的路由
  // next: 一定要调用该方法来 resolve 这个钩子。执行效果依赖 next 方法的调用参数。
