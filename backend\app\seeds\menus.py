"""
backend/app/seeds/menus.py
菜单数据填充
"""

from app.models.menu import Menu
from app.models.module import Module
from app.models.association import Role_Menu
from app.models.menu import MenuType

from app.core.database import transactional_session #自动提交

# 创建全部
def seed_menu_all():
    seed_modules()
    seed_menus()
    seed_menu_roles()

def seed_modules():
    with transactional_session() as db:
        existing = db.query(Module).count()
        if existing==0:
            admin = Module(name="系统管理",description="系统管理,系统基础模块")
            db.add_all([admin])

def seed_menu_roles():
    with transactional_session() as db:
        existing = db.query(Role_Menu).count()
        if existing==0:
            db.add_all([
                Role_Menu(role_id=1, menu_id=3),
                Role_Menu(role_id=1, menu_id=4),
                Role_Menu(role_id=1, menu_id=5),
                Role_Menu(role_id=1, menu_id=6),
                Role_Menu(role_id=1, menu_id=7),
                Role_Menu(role_id=1, menu_id=8),
            ])
            

def seed_menus():
   with transactional_session() as db:
        existing = db.query(Menu).count()
        if existing==0:
            # 创建顶级菜单
            adminmenu = Menu(
                name="系统",
                path="/system",
                meta={"title": "系统", "icon": "Setting"},
                menu_type=MenuType.menu,
                module_id=1,
                sort=99,
            )
            testmenu=Menu(
                name="测试",
                path="/test",
                meta={"title": "测试", "icon": "Test"},
                menu_type=MenuType.menu,
                module_id=1,
                requires_auth=False,
                sort=98,
            )

            db.add_all([adminmenu,testmenu])
            db.flush()
           
            # 主页
            homepage=Menu(
                name="主页",
                path="/",
                component="home/IndexPage",
                meta={"title":"主页","icon":"Home"},
                menu_type=MenuType.menu,
                module_id=1,
                sort=1
            )
           # 子集菜单
            userpage=Menu(
                parent_id=adminmenu.uid,
                name="用户",
                path="/system/user",
                component="system/user/IndexPage",
                meta={"title":"用户","icon":"User"},
                menu_type=MenuType.menu,
                module_id=1,
                sort=1
            )
            rolepage=Menu(
                parent_id=adminmenu.uid,
                name="角色",
                path="/system/role",
                component="system/role/IndexPage",
                meta={"title":"角色","icon":"Role"},
                menu_type=MenuType.menu,
                module_id=1,
                sort=2
            )
            departmentpage=Menu(
                parent_id=adminmenu.uid,
                name="部门",
                path="/system/department",
                component="system/department/IndexPage",
                meta={"title":"部门","icon":"Department"},
                menu_type=MenuType.menu,
                module_id=1,
                sort=3
            )
            menupage=Menu(
                parent_id=adminmenu.uid,
                name="菜单",
                path="/system/menu",
                component="system/menu/IndexPage",
                meta={"title":"菜单","icon":"Menu"},
                menu_type=MenuType.menu,
                module_id=1,
                sort=4
            )

            # 创建测试页面
            testpage=Menu(
                parent_id=testmenu.uid,
                name="测试页面",
                path="/test/page",
                component="test/Page",
                meta={"title":"测试页面","icon":"Test"},
                menu_type=MenuType.menu,
                module_id=1,
                requires_auth=False,
                sort=1
            )
            testpage2=Menu(
                parent_id=testmenu.uid,
                name="测试页面2",
                path="/test/page2",
                component="test/Page2",
                meta={"title":"测试页面2","icon":"Test"},
                menu_type=MenuType.menu,
                module_id=1,
                requires_auth=False,
                sort=2
            )
            db.add_all([homepage,userpage,rolepage,departmentpage,menupage,testpage,testpage2])

