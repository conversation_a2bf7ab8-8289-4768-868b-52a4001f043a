/**
 * frontend/src/router/index.ts
 * 路由管理
 */
import { createRouter, createWebHistory } from 'vue-router'

import { staticRoutes } from './static'
import { setupRouterGuards } from './guards'
import type { LoginResponseMenu } from '@/types/menu'

import { useUserStore } from '@/stores/user'
import type {Router} from 'vue-router'

// 定义路由
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),   
  routes: staticRoutes , 
})

//初始化路由
export async function initializeRoutes() {
  const userStore=useUserStore()

  //只有在用户已经登录时，才注册动态路由
  if (userStore.isLoggedIn && userStore.menus.length > 0) {
    try {
      const dynamicRoutes = await registerDynamicRoutes()
      dynamicRoutes.forEach(route  => {
        router.addRoute(route)
      })
      console.log('动态路由注册成功', router.getRoutes().length)
      return true
    } catch (error) {
      console.error('动态路由注册失败:', error)
      return false
    }
  }
  return false
}

// 导航守卫
setupRouterGuards(router)

export default router


// 登录成功后调用此函数注册动态路由
export async function initializeDynamicRoutes(menus: LoginResponseMenu[]) {
  await setupDynamicRoutes(router, menus)
}

