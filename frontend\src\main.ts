/**
 * frontend/src/main.ts
 * 应用程序入口
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { setupNaiveUI } from './plugins/naive'
import { useUserStore } from '@/stores/user'

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
setupNaiveUI(app)

// 恢复用户状态
const userStore = useUserStore()
userStore.restore()

// 直接挂载应用，动态路由将在路由守卫中按需注册
app.mount('#app')

