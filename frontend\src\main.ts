/**
 * frontend/src/main.ts
 * 应用程序入口
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { setupNaiveUI } from './plugins/naive'
import { useUserStore } from '@/stores/user'
import { registerDynamicRoutes } from './router/dynamic'

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
setupNaiveUI(app)

// 恢复用户状态
const userStore = useUserStore()
userStore.restore()

// 如果用户已登录，预先注册动态路由
async function initializeApp() {
  if (userStore.isLoggedIn && userStore.menus.length > 0) {
    console.log('用户已登录，预先注册动态路由')
    try {
      const success = await registerDynamicRoutes(router, userStore.menus)
      if (success) {
        userStore.setDynamicRoutesInitialized(true)
        console.log('预先注册动态路由成功')
      }
    } catch (error) {
      console.error('预先注册动态路由失败:', error)
    }
  }

  // 挂载应用
  app.mount('#app')
}

initializeApp()

