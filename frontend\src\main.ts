/**
 * frontend/src/main.ts
 * 应用程序入口
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { setupNaiveUI } from './plugins/naive'
import { useUserStore } from '@/stores/user' // 添加导入

import { initializeDynamicRoutes  } from './router/index'


import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
setupNaiveUI(app)

// 添加状态恢复
const userStore = useUserStore()
userStore.restore()


// 如果用户已登录，初始化动态路由
if (userStore.isLoggedIn) {
  console.log("开始恢复")
  initializeDynamicRoutes(userStore.menus).then(() => {
    console.log("加载完成")
    app.mount('#app')
  })
} else {
  app.mount('#app')
}

