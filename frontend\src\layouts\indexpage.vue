  <!-- frontend/src/layouts/IndexPage.vue -->
 <!-- 主布局 -->
  <template>
    <n-flex style="height: 100vh; display: flex; flex-direction: column;">
      <HeaderItem/>
      <n-layout style="flex: 1;display: flex;">
        <n-flex>
        <SidebarItem/>
        <n-layout-content content-style="padding: 24px; overflow:auto;">
          <router-view />
        </n-layout-content>
        </n-flex>
      </n-layout>
      <FooterItem />
    </n-flex>
  </template>
  
  <script lang="ts" setup>
  import SidebarItem from './components/SidebarItem.vue'
  import HeaderItem from './components/HeaderItem.vue'
  import FooterItem from './components/FooterItem.vue'


  </script>
  
  <style>
  
  </style>