// src/router/dynamic.ts
import type { RouteRecordRaw } from 'vue-router'
import type { LoginResponseMenu } from '@/types/menu'
import { useUserStore } from '@/stores/user'

// 预加载所有的视图组件
const views = import.meta.glob('@/views/**/*.vue')

// 解析组件路径
function resolveComponentPath(component: string): string {
  return component.replace(/\/$/, '')  
}

// 菜单转换为路由
function transformMenuToRoute(menu: LoginResponseMenu): RouteRecordRaw | null {
  if (!menu.path || !menu.name) return null

  const baseRoute: any = {
    path: menu.path,
    name: menu.name,
    meta: {
      title: menu.meta?.title ?? menu.name,
      icon: menu.meta?.icon,
      requiresAuth: menu.requires_auth ?? true
    }
  }

  // 处理组件
  if (menu.component) {
    const componentPath = resolveComponentPath(menu.component)
    const componentKey = `/src/views${componentPath}.vue`
    if (views[componentKey]) {
      baseRoute.component = views[componentKey]
    } else {
      console.warn(`找不到组件: ${componentKey}`)
    }
  }

  // 处理重定向
  if (menu.redirect) {
    baseRoute.redirect = menu.redirect
  }

  // 处理子路由
  if (menu.children && menu.children.length > 0) {
    baseRoute.children = menu.children
      .map(transformMenuToRoute)
      .filter((r): r is RouteRecordRaw => r !== null)
  }

  return baseRoute
}

// 注册动态路由
export async function registerDynamicRoutes(): Promise<RouteRecordRaw[]> {
  const userStore = useUserStore()
  
  // 将菜单转换为路由
  const routes = userStore.menus
    .map(transformMenuToRoute)
    .filter((r): r is RouteRecordRaw => r !== null)

  return routes
}