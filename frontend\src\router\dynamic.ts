// src/router/dynamic.ts
import type { RouteRecordRaw, Router } from 'vue-router'
import type { LoginResponseMenu } from '@/types/menu'

// 预加载所有的视图组件
const views = import.meta.glob('@/views/**/*.vue')

// 解析组件路径
function resolveComponentPath(component: string): string {
  return component.replace(/\/$/, '')  
}

// 菜单转换为路由
function transformMenuToRoute(menu: LoginResponseMenu): RouteRecordRaw | null {
  if (!menu.path || !menu.name) return null

  const baseRoute: Partial<RouteRecordRaw> = {
    path: menu.path,
    name: menu.name,
    meta: {
      title: menu.meta?.title ?? menu.name,
      icon: menu.meta?.icon,
      requiresAuth: menu.requires_auth ?? true
    }
  }

  // 处理组件
  if (menu.component) {
    const componentPath = resolveComponentPath(menu.component)
    const componentKey = `/src/views/${componentPath}.vue`
    if (views[componentKey]) {
      baseRoute.component = views[componentKey]
    } else {
      console.warn(`找不到组件: ${componentKey}`)
    }
  }

  // 处理重定向
  if (menu.redirect) {
    baseRoute.redirect = menu.redirect
  }

  // 处理子路由
  if (menu.children && menu.children.length > 0) {
    baseRoute.children = menu.children
      .map(transformMenuToRoute)
      .filter((r): r is RouteRecordRaw => r !== null)
  }

  return baseRoute as RouteRecordRaw
}

// 注册动态路由到路由器
export async function registerDynamicRoutes(router: Router, menus: LoginResponseMenu[]): Promise<boolean> {
  try {
    // 将菜单转换为路由
    console.log(menus)
    const routes = menus
      .map(transformMenuToRoute)
      .filter((r): r is RouteRecordRaw => r !== null)

    // 注册路由到路由器
    routes.forEach(route => {
      router.addRoute(route)
    })

    console.log('动态路由注册成功，共注册路由数量:', routes.length)
    return true
  } catch (error) {
    console.error('动态路由注册失败:', error)
    return false
  }
}

// 获取动态路由（不注册到路由器）
export async function getDynamicRoutes(menus: LoginResponseMenu[]): Promise<RouteRecordRaw[]> {
  // 将菜单转换为路由
  const routes = menus
    .map(transformMenuToRoute)
    .filter((r): r is RouteRecordRaw => r !== null)

  return routes
}