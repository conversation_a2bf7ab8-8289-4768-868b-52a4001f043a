// src/router/dynamic.ts
import type { RouteRecordRaw, Router } from 'vue-router'
import type { LoginResponseMenu } from '@/types/menu'

// 预加载所有的视图组件
const views = import.meta.glob('@/views/**/*.vue')

// 解析组件路径
function resolveComponentPath(component: string): string {
  return component.replace(/\/$/, '')  
}

// 菜单转换为路由
function transformMenuToRoute(menu: LoginResponseMenu): RouteRecordRaw[] {
  const routes: RouteRecordRaw[] = []

  // 如果有子菜单，递归处理子菜单
  if (menu.children && menu.children.length > 0) {
    menu.children.forEach(child => {
      routes.push(...transformMenuToRoute(child))
    })
  }

  // 如果当前菜单有组件，创建路由
  if (menu.component && menu.component.trim() !== '') {
    const componentPath = resolveComponentPath(menu.component)
    const componentKey = `/src/views/${componentPath}.vue`

    if (views[componentKey]) {
      const route: any = {
        path: menu.path,
        name: menu.name,
        component: views[componentKey],
        meta: {
          title: menu.meta?.title ?? menu.name,
          icon: menu.meta?.icon,
          requiresAuth: menu.requires_auth ?? true
        }
      }

      // 处理重定向
      if (menu.redirect && menu.redirect.trim() !== '') {
        route.redirect = menu.redirect
      }

      routes.push(route)
    } else {
      console.warn(`找不到组件: ${componentKey}`)
    }
  }

  return routes
}

// 注册动态路由到路由器
export async function registerDynamicRoutes(router: Router, menus: LoginResponseMenu[]): Promise<boolean> {
  try {
    console.log('开始处理菜单数据:', menus)

    // 将菜单转换为路由，扁平化处理
    const allRoutes: RouteRecordRaw[] = []
    menus.forEach(menu => {
      allRoutes.push(...transformMenuToRoute(menu))
    })

    console.log('转换后的路由:', allRoutes)

    // 将所有动态路由作为主布局的子路由注册
    allRoutes.forEach(route => {
      // 跳过根路径，因为它已经在静态路由中定义
      if (route.path === '/') {
        console.log('跳过根路径，已在静态路由中定义')
        return
      }

      // 转换为相对路径（去掉开头的 /）
      const relativePath = route.path.startsWith('/') ? route.path.slice(1) : route.path
      const childRoute = {
        ...route,
        path: relativePath
      }

      // 将其他路由作为主布局的子路由添加
      router.addRoute('layout', childRoute)
      console.log(`添加子路由: ${childRoute.path} -> ${String(childRoute.name)}`)
    })

    console.log('动态路由注册成功，共注册路由数量:', allRoutes.length)

    // 调试：打印所有注册的路由
    console.log('所有注册的路由:', router.getRoutes().map(r => ({
      name: r.name,
      path: r.path,
      hasComponent: !!r.components?.default
    })))

    return true
  } catch (error) {
    console.error('动态路由注册失败:', error)
    return false
  }
}

// 获取动态路由（不注册到路由器）
export async function getDynamicRoutes(menus: LoginResponseMenu[]): Promise<RouteRecordRaw[]> {
  // 将菜单转换为路由，扁平化处理
  const allRoutes: RouteRecordRaw[] = []
  menus.forEach(menu => {
    allRoutes.push(...transformMenuToRoute(menu))
  })

  return allRoutes
}