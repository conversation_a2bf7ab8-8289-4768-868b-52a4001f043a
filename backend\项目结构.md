app/
├── api/
│   ├── v1/
│   │   ├── user/
│   │   │   ├── schemas.py      # 用于定义 Pydantic 请求/响应模型
│   │   │   ├── routes.py       # 路由定义
│   │   │   ├── services.py     # 业务逻辑
│   │   │   ├── ---------------------------------可选文件，命名规则
│   │   │   ├── validators.py   # 放置字段级或对象级的自定义校验逻辑（比如用户名格式、邮箱唯一性）
│   │   │   ├── dependencies.py # 定义 FastAPI 的依赖注入逻辑，比如 get_current_user()、权限检查器
│   │   │   ├── constants.py    # 用户模块相关的常量定义，如默认角色、状态码映射等
│   │   │   ├── enums.py        # 定义用户状态、角色类型等枚举类，便于类型安全和文档生成
│   │   │   ├── exceptions.py   # 异常定义
│   │   │   ├── utils.py        # 放置一些通用的工具函数，如密码加密、头像生成等
│   │   │   ├── ---------------------------------
│   │   │   └── __init__.py
│   │   ├── auth/
│   │   │   ├── schemas.py
│   │   │   ├── routes.py
│   │   │   ├── services.py
│   │   │   └── __init__.py
│   │   └── __init__.py
├── core/              # 公共配置，如数据库连接、JWT工具
│   ├── cache/         # redi缓存
│   ├── config.py      # 配置文件
│   ├── database.py    # 数据库连接
│   ├── security.py    # 安全工具
│   └── __init__.py    # 入口文件
├── static/
├── seeds/             # 按模块组织种子数据,用于存放测试数据和初始化数据
├── models/            # 数据库模型
│   ├── formulas/      # 业务模块模型
│   （..........）     
│   ├── base.py        # 基础模型
│   ├── association.py # 关联模型
│   ├── user.py        # 用户模型
│   ├── role.py        # 角色模型
│   ├── menu.py        # 菜单模型
│   ├── module.py      # 模块模型
│   └── __init__.py    # 模型入口
├── __init__.py
├── main.py