<template>
  <div>
    <n-card title="路由测试页面">
      <n-space vertical>
        <n-alert type="success">
          <template #header>路由系统测试</template>
          如果您能看到这个页面，说明动态路由注册成功！
        </n-alert>
        
        <n-descriptions bordered :column="2">
          <n-descriptions-item label="当前路由">
            {{ $route.path }}
          </n-descriptions-item>
          <n-descriptions-item label="路由名称">
            {{ $route.name }}
          </n-descriptions-item>
          <n-descriptions-item label="用户登录状态">
            {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
          </n-descriptions-item>
          <n-descriptions-item label="动态路由状态">
            {{ userStore.dynamicRoutesInitialized ? '已初始化' : '未初始化' }}
          </n-descriptions-item>
        </n-descriptions>

        <n-card title="可用路由列表">
          <n-list>
            <n-list-item v-for="route in availableRoutes" :key="route.path">
              <n-thing>
                <template #header>{{ route.name }}</template>
                <template #description>{{ route.path }}</template>
                <n-button size="small" @click="navigateTo(route.path)">
                  跳转
                </n-button>
              </n-thing>
            </n-list-item>
          </n-list>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 获取所有可用路由
const availableRoutes = computed(() => {
  return router.getRoutes()
    .filter(route => route.name && route.name !== 'not-found')
    .map(route => ({
      name: route.name,
      path: route.path,
      meta: route.meta
    }))
})

function navigateTo(path: string) {
  router.push(path)
}
</script>
