/**
 * frontend/src/router/static.ts
 * 静态路由
 */

import type {  RouteRecordRaw  } from 'vue-router'

export const staticRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'layout',
    component: () => import('@/layouts/IndexPage.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
        meta: { requiresAuth: true, title: '首页' }
      }
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/system/login/IndexPage.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/common/NotFoundPage.vue')
  }
]
