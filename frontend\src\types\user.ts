/**
 * 用户相关的接口
 * frontend/src/types/user.ts
 */

import type { LoginResponseMenu, ButtonPermission } from './menu'

//用户登录使用   用于  登录页提交
export interface loginData {
  username: string;
  password: string;
}

//认证用户基本信息 TokenWithMenus 辅助
export interface AuthUser{
  uid:number
  username:string
  is_superuser:boolean
  is_active:boolean
}


//认证用户，包含token 菜单，角色信息  用于登录验证 交给pinia管理
export interface TokenWithMenus{
  token: string
  menus: LoginResponseMenu[]
  buttons: ButtonPermission[]
  user: AuthUser
}

//用户个人信息  用于前端个人主页
export interface UserInfo{
  uid:number
  username:string
  is_superuser:boolean
  is_active:boolean
  realname:string
  department_id:number
  department_name:string
  email:string | null
  phone:string | null
  avatar:string | null
  last_login_at:string | null
}